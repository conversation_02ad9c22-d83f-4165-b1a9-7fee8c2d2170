#!/usr/bin/env python3
"""
Direct CLI test
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.getcwd())

def test_cli_help():
    """Test CLI help command"""
    try:
        from exo_Piper.cli import cli
        from click.testing import CliRunner
        
        runner = CliRunner()
        result = runner.invoke(cli, ['--help'])
        
        print("CLI Help Output:")
        print("=" * 50)
        print(result.output)
        print("=" * 50)
        
        if result.exit_code == 0:
            print("✅ CLI help command works")
            return True
        else:
            print(f"❌ CLI help failed with exit code {result.exit_code}")
            return False
            
    except Exception as e:
        print(f"❌ CLI help test failed: {e}")
        return False

def test_cli_init():
    """Test CLI init command"""
    try:
        from exo_Piper.cli import cli
        from click.testing import CliRunner
        import tempfile
        import os
        
        runner = CliRunner()
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Change to temp directory
            old_cwd = os.getcwd()
            os.chdir(temp_dir)
            
            try:
                result = runner.invoke(cli, ['init'])
                
                print("CLI Init Output:")
                print("=" * 50)
                print(result.output)
                print("=" * 50)
                
                if result.exit_code == 0:
                    print("✅ CLI init command works")
                    
                    # Check if config file was created
                    config_file = os.path.join(temp_dir, "perfconfig.yaml")
                    if os.path.exists(config_file):
                        print("✅ Configuration file created")
                        return True
                    else:
                        print("❌ Configuration file not created")
                        return False
                else:
                    print(f"❌ CLI init failed with exit code {result.exit_code}")
                    return False
                    
            finally:
                os.chdir(old_cwd)
            
    except Exception as e:
        print(f"❌ CLI init test failed: {e}")
        return False

def main():
    """Run CLI tests"""
    print("=" * 50)
    print("Exo Piper CLI Direct Test")
    print("=" * 50)
    
    tests = [test_cli_help, test_cli_init]
    passed = 0
    
    for test in tests:
        print(f"\nRunning {test.__name__}...")
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Results: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 CLI tests passed!")
        return 0
    else:
        print("❌ Some CLI tests failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
