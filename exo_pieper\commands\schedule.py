"""
Scheduling and automation CLI commands
"""

import click
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from datetime import datetime

from ..core.config import Config
from ..core.client import ExoPiperClient

console = Console()

@click.group(name='schedule')
def schedule_group():
    """Scheduling and automation commands"""
    pass

@schedule_group.command()
@click.option('--workloads', '-w', help='Comma-separated list of workloads')
@click.option('--profile', '-p', default='default', help='Configuration profile')
@click.option('--frequency', '-f', type=click.Choice(['daily', 'weekly', 'monthly']), 
              required=True, help='Schedule frequency')
@click.option('--time', '-t', default='02:00', help='Execution time (HH:MM format)')
@click.option('--enabled/--disabled', default=True, help='Enable schedule')
@click.pass_context
def create(ctx, workloads, profile, frequency, time, enabled):
    """Create a new benchmark schedule"""
    config = ctx.obj['config']
    
    if not config.is_authenticated():
        console.print(Panel(
            "❌ Not authenticated. Please run [bold]perf-audit auth login[/bold] first.",
            title="Authentication Required",
            border_style="red"
        ))
        return
    
    try:
        # Parse workloads
        if workloads:
            workload_list = [w.strip() for w in workloads.split(',')]
        else:
            workload_list = config.get_default_workloads()
        
        # Validate time format
        try:
            datetime.strptime(time, '%H:%M')
        except ValueError:
            console.print(Panel(
                "❌ Invalid time format. Use HH:MM (e.g., 14:30)",
                title="Validation Error",
                border_style="red"
            ))
            return
        
        client = ExoPiperClient(config)
        
        schedule_config = {
            "workloads": workload_list,
            "profile": profile,
            "frequency": frequency,
            "time": time,
            "enabled": enabled
        }
        
        result = client._make_request("POST", "/schedules", json=schedule_config)
        
        console.print(Panel(
            f"✅ Schedule created successfully\n"
            f"ID: [bold]{result['schedule_id']}[/bold]\n"
            f"Frequency: {frequency}\n"
            f"Time: {time}\n"
            f"Workloads: {', '.join(workload_list)}\n"
            f"Status: {'Enabled' if enabled else 'Disabled'}",
            title="Schedule Created",
            border_style="green"
        ))
        
    except Exception as e:
        console.print(Panel(
            f"❌ Failed to create schedule: {str(e)}",
            title="Error",
            border_style="red"
        ))

@schedule_group.command()
@click.pass_context
def list(ctx):
    """List all scheduled benchmarks"""
    config = ctx.obj['config']
    
    if not config.is_authenticated():
        console.print(Panel(
            "❌ Not authenticated. Please run [bold]perf-audit auth login[/bold] first.",
            title="Authentication Required",
            border_style="red"
        ))
        return
    
    try:
        client = ExoPiperClient(config)
        schedules = client._make_request("GET", "/schedules")
        
        if not schedules.get('schedules'):
            console.print(Panel(
                "No scheduled benchmarks found",
                title="Schedules",
                border_style="yellow"
            ))
            return
        
        table = Table(title="Scheduled Benchmarks")
        table.add_column("ID", style="cyan")
        table.add_column("Frequency", style="green")
        table.add_column("Time", style="blue")
        table.add_column("Workloads", style="yellow")
        table.add_column("Next Run", style="magenta")
        table.add_column("Status", style="white")
        
        for schedule in schedules['schedules']:
            workloads = ', '.join(schedule.get('workloads', []))
            if len(workloads) > 30:
                workloads = workloads[:27] + "..."
            
            next_run = schedule.get('next_run', 'Not scheduled')
            if next_run != 'Not scheduled':
                try:
                    next_run_date = datetime.fromisoformat(next_run.replace('Z', '+00:00'))
                    next_run = next_run_date.strftime('%m-%d %H:%M')
                except:
                    pass
            
            status = "✅ Enabled" if schedule.get('enabled') else "❌ Disabled"
            
            table.add_row(
                schedule['id'][:8],
                schedule['frequency'],
                schedule['time'],
                workloads,
                next_run,
                status
            )
        
        console.print(table)
        console.print("Use [bold]perf-audit schedule show <id>[/bold] for details")
        
    except Exception as e:
        console.print(Panel(
            f"❌ Failed to list schedules: {str(e)}",
            title="Error",
            border_style="red"
        ))

@schedule_group.command()
@click.argument('schedule_id')
@click.pass_context
def show(ctx, schedule_id):
    """Show detailed schedule information"""
    config = ctx.obj['config']
    
    if not config.is_authenticated():
        console.print(Panel(
            "❌ Not authenticated. Please run [bold]perf-audit auth login[/bold] first.",
            title="Authentication Required",
            border_style="red"
        ))
        return
    
    try:
        client = ExoPiperClient(config)
        schedule = client._make_request("GET", f"/schedules/{schedule_id}")
        
        table = Table(title=f"Schedule Details")
        table.add_column("Property", style="cyan")
        table.add_column("Value", style="white")
        
        created_date = datetime.fromisoformat(schedule['created_at'].replace('Z', '+00:00'))
        
        table.add_row("ID", schedule['id'])
        table.add_row("Frequency", schedule['frequency'])
        table.add_row("Time", schedule['time'])
        table.add_row("Profile", schedule['profile'])
        table.add_row("Workloads", ', '.join(schedule['workloads']))
        table.add_row("Status", "Enabled" if schedule['enabled'] else "Disabled")
        table.add_row("Created", created_date.strftime('%Y-%m-%d %H:%M:%S UTC'))
        
        if schedule.get('next_run'):
            next_run_date = datetime.fromisoformat(schedule['next_run'].replace('Z', '+00:00'))
            table.add_row("Next Run", next_run_date.strftime('%Y-%m-%d %H:%M:%S UTC'))
        
        if schedule.get('last_run'):
            last_run_date = datetime.fromisoformat(schedule['last_run'].replace('Z', '+00:00'))
            table.add_row("Last Run", last_run_date.strftime('%Y-%m-%d %H:%M:%S UTC'))
        
        table.add_row("Total Executions", str(schedule.get('execution_count', 0)))
        table.add_row("Success Rate", f"{schedule.get('success_rate', 0):.1f}%")
        
        console.print(table)
        
    except Exception as e:
        console.print(Panel(
            f"❌ Failed to show schedule: {str(e)}",
            title="Error",
            border_style="red"
        ))

@schedule_group.command()
@click.argument('schedule_id')
@click.option('--confirm', is_flag=True, help='Skip confirmation prompt')
@click.pass_context
def delete(ctx, schedule_id, confirm):
    """Delete a scheduled benchmark"""
    config = ctx.obj['config']
    
    if not config.is_authenticated():
        console.print(Panel(
            "❌ Not authenticated. Please run [bold]perf-audit auth login[/bold] first.",
            title="Authentication Required",
            border_style="red"
        ))
        return
    
    try:
        if not confirm:
            if not click.confirm(f"Delete schedule {schedule_id}?"):
                console.print("Operation cancelled.")
                return
        
        client = ExoPiperClient(config)
        client._make_request("DELETE", f"/schedules/{schedule_id}")
        
        console.print(Panel(
            f"✅ Schedule {schedule_id} deleted successfully",
            title="Schedule Deleted",
            border_style="green"
        ))
        
    except Exception as e:
        console.print(Panel(
            f"❌ Failed to delete schedule: {str(e)}",
            title="Error",
            border_style="red"
        ))

@schedule_group.command()
@click.argument('schedule_id')
@click.option('--enable/--disable', default=True, help='Enable or disable schedule')
@click.pass_context
def toggle(ctx, schedule_id, enable):
    """Enable or disable a scheduled benchmark"""
    config = ctx.obj['config']
    
    if not config.is_authenticated():
        console.print(Panel(
            "❌ Not authenticated. Please run [bold]perf-audit auth login[/bold] first.",
            title="Authentication Required",
            border_style="red"
        ))
        return
    
    try:
        client = ExoPiperClient(config)
        
        update_data = {"enabled": enable}
        client._make_request("PATCH", f"/schedules/{schedule_id}", json=update_data)
        
        action = "enabled" if enable else "disabled"
        console.print(Panel(
            f"✅ Schedule {schedule_id} {action} successfully",
            title="Schedule Updated",
            border_style="green"
        ))
        
    except Exception as e:
        console.print(Panel(
            f"❌ Failed to update schedule: {str(e)}",
            title="Error",
            border_style="red"
        ))
