#!/usr/bin/env python3
"""
Simple test script for Exo Piper CLI
"""

import subprocess
import sys
import tempfile
import os
from pathlib import Path

def run_command(cmd, cwd=None):
    """Run a command and return the result"""
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True, 
            cwd=cwd
        )
        return result.returncode, result.stdout, result.stderr
    except Exception as e:
        return 1, "", str(e)

def test_cli_installation():
    """Test that the CLI can be imported and basic commands work"""
    print("Testing Exo Piper CLI...")
    
    # Test import
    try:
        import exo_Piper
        print("✅ Package import successful")
    except ImportError as e:
        print(f"❌ Package import failed: {e}")
        return False
    
    # Test CLI entry point
    returncode, stdout, stderr = run_command("python -m exo_Piper.cli --help")
    if returncode == 0:
        print("✅ CLI help command works")
    else:
        print(f"❌ CLI help failed: {stderr}")
        return False
    
    # Test version
    returncode, stdout, stderr = run_command("python -m exo_Piper.cli --version")
    if returncode == 0:
        print("✅ Version command works")
    else:
        print(f"❌ Version command failed: {stderr}")
        return False
    
    return True

def test_config_initialization():
    """Test configuration initialization"""
    print("\nTesting configuration initialization...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Test init command
        returncode, stdout, stderr = run_command(
            "python -m exo_Piper.cli init", 
            cwd=temp_dir
        )
        
        if returncode == 0:
            print("✅ Init command works")
            
            # Check if config file was created
            config_file = Path(temp_dir) / "perfconfig.yaml"
            if config_file.exists():
                print("✅ Configuration file created")
                
                # Try to load and validate config
                try:
                    from exo_Piper.core.config import Config
                    config = Config(config_path=str(config_file))
                    profiles = config.get_profiles()
                    print(f"✅ Configuration loaded with profiles: {profiles}")
                    return True
                except Exception as e:
                    print(f"❌ Configuration validation failed: {e}")
                    return False
            else:
                print("❌ Configuration file not created")
                return False
        else:
            print(f"❌ Init command failed: {stderr}")
            return False

def test_workload_commands():
    """Test workload management commands"""
    print("\nTesting workload commands...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Initialize config first
        run_command("python -m exo_Piper.cli init", cwd=temp_dir)
        
        # Test workload list
        returncode, stdout, stderr = run_command(
            "python -m exo_Piper.cli workloads list", 
            cwd=temp_dir
        )
        
        if returncode == 0:
            print("✅ Workload list command works")
            return True
        else:
            print(f"❌ Workload list failed: {stderr}")
            return False

def test_benchmark_script():
    """Test the mergesort benchmark script"""
    print("\nTesting benchmark script...")
    
    try:
        from exo_Piper.benchmarks.mergesort import benchmark_mergesort
        
        # Test with minimal config
        config = {
            "min_size": 100,
            "max_size": 1000,
            "steps": 3,
            "iterations": 2,
            "data_type": "random"
        }
        
        results = benchmark_mergesort(config)
        
        if (results and 
            "input_sizes" in results and 
            "execution_times" in results and
            len(results["input_sizes"]) == 3):
            print("✅ Mergesort benchmark works")
            print(f"   Sizes tested: {results['input_sizes']}")
            print(f"   Times: {[f'{t:.6f}s' for t in results['execution_times']]}")
            return True
        else:
            print("❌ Mergesort benchmark returned invalid results")
            return False
            
    except Exception as e:
        print(f"❌ Mergesort benchmark failed: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 50)
    print("Exo Piper CLI Test Suite")
    print("=" * 50)
    
    tests = [
        test_cli_installation,
        test_config_initialization,
        test_workload_commands,
        test_benchmark_script
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! CLI is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Check the output above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
