<!-- Cabeçalho com logo centralizada -->
<div align="center">
  <img src="logo.png" alt="Exo Piper Logo" width="300" />
  <h1>Exo Piper CLI</h1>
  <p><strong>Ferramenta de Auditoria de Performance com Teorema da Relatividade de Complexidade</strong></p>

  <!-- Badges -->
  <p>
    <img src="https://img.shields.io/badge/Versão-1.0.0-blue" alt="Versão" />
    <img src="https://img.shields.io/badge/Licença-MIT-green" alt="Licença" />
    <img src="https://img.shields.io/badge/Docker-Compatível-2496ED?logo=docker" alt="Docker" />
    <img src="https://img.shields.io/badge/Python-3.7+-yellow?logo=python" alt="Python" />
  </p>

  <p>
    <a href="#instalação">Instalação</a> •
    <a href="#início-rápido"><PERSON><PERSON><PERSON></a> •
    <a href="#configuração">Configuração</a> •
    <a href="#comandos-disponíveis">Comandos</a> •
    <a href="#teorema-da-relatividade-de-complexidade">Teorema</a> •
    <a href="#planos-de-preço">Preços</a> •
    <a href="#suporte">Suporte</a>
  </p>
</div>

---

## 📋 Visão Geral

Uma ferramenta de linha de comando abrangente para auditoria de performance que implementa o **Teorema da Relatividade de Complexidade** para análise automatizada de benchmarks e avaliação de escalabilidade.

### ✨ Funcionalidades Principais

<table>
  <tr>
    <td>🚀 <b>Benchmarking Automatizado</b></td>
    <td>Execute benchmarks de performance usando agentes Docker</td>
  </tr>
  <tr>
    <td>📊 <b>Análise de Complexidade</b></td>
    <td>Calcule λ (lambda) e p (expoente) usando regressão log-log</td>
  </tr>
  <tr>
    <td>🔄 <b>Monitoramento Contínuo</b></td>
    <td>Agende auditorias regulares de performance</td>
  </tr>
  <tr>
    <td>📈 <b>Relatórios Detalhados</b></td>
    <td>Gere relatórios de performance com visualizações</td>
  </tr>
  <tr>
    <td>🎯 <b>Sistema de Alertas</b></td>
    <td>Receba notificações quando a performance degradar</td>
  </tr>
  <tr>
    <td>🐳 <b>Integração Docker</b></td>
    <td>Ambientes de execução isolados para benchmarks</td>
  </tr>
  <tr>
    <td>🌐 <b>Integração SaaS</b></td>
    <td>Conecte-se ao serviço em nuvem Exo Piper</td>
  </tr>
</table>

## 📥 Instalação

```bash
pip install exo-Piper
```

## 🚀 Início Rápido

### 1. Autenticação

```bash
# Login no serviço Exo Piper
perf-audit login

# Verificar status de autenticação
perf-audit auth whoami
```

### 2. Inicializar Projeto

```bash
# Criar configuração no diretório atual
perf-audit init

# Isso cria o arquivo perfconfig.yaml com cargas de trabalho padrão
```

### 3. Executar Benchmarks

```bash
# Executar todas as cargas de trabalho configuradas
perf-audit run

# Executar cargas de trabalho específicas
perf-audit run --workloads mergesort,3sat

# Usar perfil diferente
perf-audit run --profile quick
```

### 4. Visualizar Resultados

```bash
# Verificar status da conta
perf-audit status

# Listar relatórios
perf-audit reports list

# Baixar relatório
perf-audit reports download <report-id>
```

## ⚙️ Configuração

O arquivo `perfconfig.yaml` define suas cargas de trabalho de benchmark:

```yaml
version: "1.0"
project:
  name: "my-performance-audit"
  description: "Performance auditing project"

profiles:
  default:
    docker_image: "exoPiper/agent:latest"
    workloads:
      - name: "mergesort"
        type: "sorting"
        parameters:
          min_size: 1000
          max_size: 100000
          steps: 10
        enabled: true
        timeout: 300
        iterations: 5
      
      - name: "3sat"
        type: "sat_solver"
        parameters:
          min_variables: 50
          max_variables: 500
          steps: 8
        enabled: true
        timeout: 600
        iterations: 3

settings:
  parallel_jobs: 2
  output_format: "json"
  log_level: "info"
```

## 🛠️ Comandos Disponíveis

<details>
  <summary><b>🔑 Autenticação</b></summary>
  <ul>
    <li><code>perf-audit login</code> - Autenticar com chave API</li>
    <li><code>perf-audit auth logout</code> - Remover credenciais armazenadas</li>
    <li><code>perf-audit auth whoami</code> - Mostrar informações do usuário atual</li>
    <li><code>perf-audit auth upgrade</code> - Atualizar plano da conta</li>
  </ul>
</details>

<details>
  <summary><b>📁 Gerenciamento de Projeto</b></summary>
  <ul>
    <li><code>perf-audit init</code> - Inicializar configuração do projeto</li>
    <li><code>perf-audit status</code> - Mostrar status da conta e projeto</li>
  </ul>
</details>

<details>
  <summary><b>⚡ Execução de Benchmark</b></summary>
  <ul>
    <li><code>perf-audit run</code> - Executar benchmarks</li>
    <li><code>perf-audit run --workloads &lt;lista&gt;</code> - Executar cargas de trabalho específicas</li>
    <li><code>perf-audit run --profile &lt;n&gt;</code> - Usar perfil específico</li>
    <li><code>perf-audit run --dry-run</code> - Mostrar o que seria executado</li>
  </ul>
</details>

<details>
  <summary><b>📋 Gerenciamento de Cargas de Trabalho</b></summary>
  <ul>
    <li><code>perf-audit workloads list</code> - Listar cargas de trabalho configuradas</li>
    <li><code>perf-audit workloads add &lt;n&gt;</code> - Adicionar nova carga de trabalho</li>
    <li><code>perf-audit workloads remove &lt;n&gt;</code> - Remover carga de trabalho</li>
    <li><code>perf-audit workloads show &lt;n&gt;</code> - Mostrar detalhes da carga de trabalho</li>
    <li><code>perf-audit workloads templates</code> - Listar templates disponíveis</li>
  </ul>
</details>

<details>
  <summary><b>📊 Relatórios</b></summary>
  <ul>
    <li><code>perf-audit reports list</code> - Listar relatórios disponíveis</li>
    <li><code>perf-audit reports show &lt;id&gt;</code> - Mostrar detalhes do relatório</li>
    <li><code>perf-audit reports download &lt;id&gt;</code> - Baixar arquivo do relatório</li>
    <li><code>perf-audit reports history</code> - Mostrar histórico de benchmark</li>
  </ul>
</details>

<details>
  <summary><b>🕒 Agendamento</b></summary>
  <ul>
    <li><code>perf-audit schedule create</code> - Criar benchmark agendado</li>
    <li><code>perf-audit schedule list</code> - Listar benchmarks agendados</li>
    <li><code>perf-audit schedule show &lt;id&gt;</code> - Mostrar detalhes do agendamento</li>
    <li><code>perf-audit schedule delete &lt;id&gt;</code> - Excluir agendamento</li>
    <li><code>perf-audit schedule toggle &lt;id&gt;</code> - Habilitar/desabilitar agendamento</li>
  </ul>
</details>

## 🧮 Teorema da Relatividade de Complexidade

<div align="center">
  <img src="https://img.shields.io/badge/T(n)=λ×n^p-formula-blue" alt="Formula" />
</div>

A CLI implementa o protocolo do Teorema da Relatividade de Complexidade:

1. **Execução de Benchmark**: Executa cargas de trabalho com tamanhos de entrada variáveis
2. **Subtração de Overhead**: Remove overhead de tempo constante das medições
3. **Regressão Log-Log**: Ajusta T(n) = λ × n^p para extrair métricas de complexidade
4. **Validação de Invariância**: Verifica que o expoente p permanece estável entre hardwares

### Métricas Principais

<table>
  <tr>
    <th>Métrica</th>
    <th>Descrição</th>
  </tr>
  <tr>
    <td><b>λ (Lambda)</b></td>
    <td>Fator constante dependente do hardware</td>
  </tr>
  <tr>
    <td><b>p (Expoente)</b></td>
    <td>Expoente de complexidade do algoritmo (invariante ao hardware)</td>
  </tr>
  <tr>
    <td><b>R²</b></td>
    <td>Qualidade do ajuste da regressão</td>
  </tr>
  <tr>
    <td><b>Overhead</b></td>
    <td>Linha de base de tempo constante</td>
  </tr>
</table>

## 🐳 Agente Docker

Os benchmarks são executados em contêineres Docker isolados usando a imagem `exoPiper/agent`:

- **Isolamento**: Sem acesso à rede durante a execução
- **Limites de Recursos**: Alocação controlada de CPU e memória
- **Reprodutibilidade**: Ambiente de execução consistente
- **Segurança**: Execução em sandbox evita interferência do sistema

## 💰 Planos de Preço

<table>
  <tr>
    <th>Plano</th>
    <th>Preço</th>
    <th>Recursos</th>
  </tr>
  <tr>
    <td><b>Free</b></td>
    <td>Gratuito</td>
    <td>2 cargas de trabalho, retenção de 7 dias</td>
  </tr>
  <tr>
    <td><b>Pro</b></td>
    <td>$29/mês</td>
    <td>5 cargas de trabalho, retenção de 30 dias, alertas</td>
  </tr>
  <tr>
    <td><b>Team</b></td>
    <td>$79/mês</td>
    <td>20 cargas de trabalho, retenção de 90 dias, multi-usuário, SSO</td>
  </tr>
</table>

## 📞 Suporte

<table>
  <tr>
    <td>📚 <b>Documentação</b></td>
    <td><a href="https://docs.exoPiper.io">https://docs.exoPiper.io</a></td>
  </tr>
  <tr>
    <td>🐛 <b>Problemas</b></td>
    <td><a href="https://github.com/exoPiper/cli/issues">https://github.com/exoPiper/cli/issues</a></td>
  </tr>
  <tr>
    <td>✉️ <b>Email</b></td>
    <td><a href="mailto:<EMAIL>"><EMAIL></a></td>
  </tr>
</table>

## 📄 Licença

<div align="center">
  <p>MIT License - veja o arquivo LICENSE para detalhes.</p>
  <p>Copyright © 2023 Exo Piper</p>
</div>
