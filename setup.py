#!/usr/bin/env python3
"""
Setup script for Exo Piper CLI - Performance Auditing Tool
"""

from setuptools import setup, find_packages
import os

# Read the README file for long description
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "Exo Piper - Performance Auditing CLI Tool"

# Read requirements
def read_requirements():
    req_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    if os.path.exists(req_path):
        with open(req_path, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    return []

setup(
    name="exo-Piper",
    version="0.1.0",
    author="Exo Piper Team",
    author_email="<EMAIL>",
    description="Performance auditing CLI tool implementing Complexity Relativity Theorem",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/exoPiper/cli",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Software Development :: Testing",
        "Topic :: System :: Benchmark",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    entry_points={
        "console_scripts": [
            "perf-audit=exo_Piper.cli:main",
            "exo-Piper=exo_Piper.cli:main",
        ],
    },
    include_package_data=True,
    package_data={
        "exo_Piper": [
            "templates/*.yaml",
            "benchmarks/*.py",
            "docker/*.dockerfile",
        ],
    },
    zip_safe=False,
)
