#!/usr/bin/env python3
"""
Simple test to verify CLI structure
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.getcwd())

def test_imports():
    """Test basic imports"""
    try:
        print("Testing imports...")
        
        # Test core modules
        from exo_Piper.core.config import Config
        print("✅ Config import successful")
        
        from exo_Piper.core.client import ExoPiperClient
        print("✅ Client import successful")
        
        # Test CLI module (without dependencies that might not be installed)
        import exo_Piper.cli
        print("✅ CLI module import successful")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_config():
    """Test configuration functionality"""
    try:
        print("\nTesting configuration...")
        
        from exo_Piper.core.config import Config
        
        # Test config creation
        config = Config()
        print("✅ Config object created")
        
        # Test default values
        assert config.DEFAULT_API_URL == "https://api.exoPiper.io"
        print("✅ Default API URL correct")
        
        return True
        
    except Exception as e:
        print(f"❌ Config test failed: {e}")
        return False

def test_benchmark():
    """Test benchmark script"""
    try:
        print("\nTesting benchmark...")
        
        from exo_Piper.benchmarks.mergesort import mergesort, generate_test_data
        
        # Test mergesort function
        test_data = [3, 1, 4, 1, 5, 9, 2, 6]
        sorted_data = mergesort(test_data)
        expected = [1, 1, 2, 3, 4, 5, 6, 9]
        
        assert sorted_data == expected, f"Expected {expected}, got {sorted_data}"
        print("✅ Mergesort function works")
        
        # Test data generation
        data = generate_test_data(100, "random")
        assert len(data) == 100
        print("✅ Test data generation works")
        
        return True
        
    except Exception as e:
        print(f"❌ Benchmark test failed: {e}")
        return False

def main():
    """Run tests"""
    print("=" * 40)
    print("Exo Piper CLI Simple Test")
    print("=" * 40)
    
    tests = [test_imports, test_config, test_benchmark]
    passed = 0
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 40)
    print(f"Results: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All basic tests passed!")
        print("\nNext steps:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Install package: pip install -e .")
        print("3. Test CLI: perf-audit --help")
        return 0
    else:
        print("❌ Some tests failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
