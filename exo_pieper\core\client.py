"""
API client for communicating with Exo Piper service
"""

import httpx
import json
import time
from typing import Dict, List, Optional, Any
from pathlib import Path
import click
from rich.progress import Progress, SpinnerColumn, TextColumn

from .config import Config

class ExoPiperClient:
    """HTTP client for Exo Piper API"""
    
    def __init__(self, config: Config):
        self.config = config
        self.api_url = config.api_url
        self.api_key = config.get_api_key()
        
        # Initialize HTTP client with timeout and retry settings
        self.client = httpx.Client(
            timeout=30.0,
            headers={
                "User-Agent": "exo-Piper-cli/0.1.0",
                "Content-Type": "application/json"
            }
        )
        
        if self.api_key:
            self.client.headers["Authorization"] = f"Bearer {self.api_key}"
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.client.close()
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make HTTP request with error handling"""
        url = f"{self.api_url}{endpoint}"
        
        try:
            response = self.client.request(method, url, **kwargs)
            response.raise_for_status()
            
            if response.headers.get("content-type", "").startswith("application/json"):
                return response.json()
            else:
                return {"data": response.text}
                
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 401:
                raise click.ClickException("Authentication failed. Please check your API key.")
            elif e.response.status_code == 403:
                raise click.ClickException("Access denied. Check your plan limits.")
            elif e.response.status_code == 429:
                raise click.ClickException("Rate limit exceeded. Please try again later.")
            else:
                try:
                    error_data = e.response.json()
                    error_msg = error_data.get("detail", str(e))
                except:
                    error_msg = str(e)
                raise click.ClickException(f"API error: {error_msg}")
                
        except httpx.RequestError as e:
            raise click.ClickException(f"Network error: {str(e)}")
    
    def authenticate(self, api_key: str) -> Dict[str, Any]:
        """Authenticate with the API and return user info"""
        # Temporarily set API key for this request
        headers = {"Authorization": f"Bearer {api_key}"}
        
        response = self.client.get(
            f"{self.api_url}/auth/me",
            headers=headers
        )
        response.raise_for_status()
        
        user_info = response.json()
        
        # Update client headers for future requests
        self.api_key = api_key
        self.client.headers["Authorization"] = f"Bearer {api_key}"
        
        return user_info
    
    def get_status(self) -> Dict[str, Any]:
        """Get current account status"""
        return self._make_request("GET", "/auth/me")
    
    def create_job(self, workloads: List[str], profile: str, metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """Create a new benchmark job"""
        payload = {
            "workloads": workloads,
            "profile": profile,
            "metadata": metadata or {}
        }
        
        return self._make_request("POST", "/jobs", json=payload)
    
    def get_job(self, job_id: str) -> Dict[str, Any]:
        """Get job status and results"""
        return self._make_request("GET", f"/jobs/{job_id}")
    
    def wait_for_job(self, job_id: str, timeout: int = 3600) -> Dict[str, Any]:
        """Wait for job completion with progress indication"""
        start_time = time.time()
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            transient=True,
        ) as progress:
            task = progress.add_task("Waiting for job completion...", total=None)
            
            while time.time() - start_time < timeout:
                job_data = self.get_job(job_id)
                status = job_data.get("status")
                
                if status == "completed":
                    progress.update(task, description="✅ Job completed successfully")
                    return job_data
                elif status == "failed":
                    progress.update(task, description="❌ Job failed")
                    raise click.ClickException(f"Job failed: {job_data.get('error', 'Unknown error')}")
                elif status in ["running", "pending"]:
                    progress.update(task, description=f"Job {status}... ({int(time.time() - start_time)}s)")
                    time.sleep(5)
                else:
                    progress.update(task, description=f"Job status: {status}")
                    time.sleep(2)
            
            raise click.ClickException(f"Job timeout after {timeout} seconds")
    
    def upload_benchmark_data(self, job_id: str, workload: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Upload benchmark results for a specific workload"""
        payload = {
            "job_id": job_id,
            "workload": workload,
            "data": data
        }
        
        return self._make_request("POST", f"/jobs/{job_id}/results", json=payload)
    
    def get_reports(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get list of available reports"""
        params = {"limit": limit}
        response = self._make_request("GET", "/reports", params=params)
        return response.get("reports", [])
    
    def get_report(self, report_id: str) -> Dict[str, Any]:
        """Get specific report details"""
        return self._make_request("GET", f"/reports/{report_id}")
    
    def download_report(self, report_id: str, output_path: Path) -> None:
        """Download report file"""
        response = self.client.get(f"{self.api_url}/reports/{report_id}/download")
        response.raise_for_status()
        
        with open(output_path, 'wb') as f:
            f.write(response.content)
    
    def get_workload_templates(self) -> List[Dict[str, Any]]:
        """Get available workload templates"""
        response = self._make_request("GET", "/workloads/templates")
        return response.get("templates", [])
    
    def validate_workload_config(self, workload_config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate workload configuration"""
        return self._make_request("POST", "/workloads/validate", json=workload_config)
    
    def get_benchmark_history(self, workload: str = None, days: int = 30) -> List[Dict[str, Any]]:
        """Get benchmark execution history"""
        params = {"days": days}
        if workload:
            params["workload"] = workload
            
        response = self._make_request("GET", "/benchmarks/history", params=params)
        return response.get("history", [])
    
    def create_alert(self, alert_config: Dict[str, Any]) -> Dict[str, Any]:
        """Create performance alert"""
        return self._make_request("POST", "/alerts", json=alert_config)
    
    def get_alerts(self) -> List[Dict[str, Any]]:
        """Get active alerts"""
        response = self._make_request("GET", "/alerts")
        return response.get("alerts", [])
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get system information and service status"""
        return self._make_request("GET", "/system/info")
