"""
Authentication-related CLI commands
"""

import click
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

from ..core.config import Config
from ..core.client import ExoPiperClient

console = Console()

@click.group(name='auth')
def auth_group():
    """Authentication and account management commands"""
    pass

@auth_group.command()
@click.option('--api-key', prompt=True, hide_input=True, help='Your Exo Piper API key')
@click.pass_context
def login(ctx, api_key):
    """Authenticate with Exo Piper service"""
    config = ctx.obj['config']
    client = ExoPiperClient(config)
    
    try:
        # Validate API key
        user_info = client.authenticate(api_key)
        
        # Save credentials
        config.save_credentials(api_key)
        
        console.print(Panel(
            f"✅ Successfully authenticated as [bold]{user_info['email']}[/bold]\n"
            f"Plan: [bold]{user_info['plan']}[/bold]\n"
            f"Workloads remaining: [bold]{user_info['workloads_remaining']}/{user_info['workloads_limit']}[/bold]",
            title="Authentication Success",
            border_style="green"
        ))
        
    except Exception as e:
        console.print(Panel(
            f"❌ Authentication failed: {str(e)}",
            title="Authentication Error",
            border_style="red"
        ))
        raise click.Abort()

@auth_group.command()
@click.pass_context
def logout(ctx):
    """Remove stored authentication credentials"""
    config = ctx.obj['config']
    
    if config.CREDENTIALS_FILE.exists():
        config.CREDENTIALS_FILE.unlink()
        console.print(Panel(
            "✅ Successfully logged out",
            title="Logout",
            border_style="green"
        ))
    else:
        console.print(Panel(
            "ℹ️ No stored credentials found",
            title="Logout",
            border_style="blue"
        ))

@auth_group.command()
@click.pass_context
def whoami(ctx):
    """Show current authentication status"""
    config = ctx.obj['config']
    
    if not config.is_authenticated():
        console.print(Panel(
            "❌ Not authenticated. Please run [bold]perf-audit auth login[/bold] first.",
            title="Authentication Status",
            border_style="red"
        ))
        return
    
    try:
        client = ExoPiperClient(config)
        user_info = client.get_status()
        
        table = Table(title="Authentication Status")
        table.add_column("Property", style="cyan")
        table.add_column("Value", style="white")
        
        table.add_row("Status", "✅ Authenticated")
        table.add_row("Email", user_info['email'])
        table.add_row("Plan", user_info['plan'])
        table.add_row("API URL", config.api_url)
        table.add_row("Workloads Used", f"{user_info['workloads_used']}/{user_info['workloads_limit']}")
        table.add_row("Data Retention", f"{user_info['retention_days']} days")
        
        console.print(table)
        
    except Exception as e:
        console.print(Panel(
            f"❌ Failed to get authentication status: {str(e)}",
            title="Authentication Error",
            border_style="red"
        ))

@auth_group.command()
@click.option('--plan', type=click.Choice(['free', 'pro', 'team']), help='Target plan')
@click.pass_context
def upgrade(ctx, plan):
    """Upgrade your account plan"""
    config = ctx.obj['config']
    
    if not config.is_authenticated():
        console.print(Panel(
            "❌ Not authenticated. Please run [bold]perf-audit auth login[/bold] first.",
            title="Authentication Required",
            border_style="red"
        ))
        return
    
    try:
        client = ExoPiperClient(config)
        current_status = client.get_status()
        current_plan = current_status['plan']
        
        if not plan:
            # Show upgrade options
            table = Table(title="Available Plans")
            table.add_column("Plan", style="cyan")
            table.add_column("Price", style="green")
            table.add_column("Workloads", style="yellow")
            table.add_column("Retention", style="blue")
            table.add_column("Features", style="white")
            
            table.add_row("Free", "$0/month", "2", "7 days", "Basic benchmarking")
            table.add_row("Pro", "$29/month", "5", "30 days", "Alerts, Advanced reports")
            table.add_row("Team", "$79/month", "20", "90 days", "Multi-user, SSO, Priority support")
            
            console.print(table)
            console.print(f"\nCurrent plan: [bold]{current_plan}[/bold]")
            console.print("\nTo upgrade, run: [bold]perf-audit auth upgrade --plan <plan_name>[/bold]")
            return
        
        if plan == current_plan.lower():
            console.print(Panel(
                f"ℹ️ You are already on the {plan} plan",
                title="Plan Status",
                border_style="blue"
            ))
            return
        
        # Generate upgrade URL
        upgrade_info = client._make_request("POST", "/billing/upgrade", json={"plan": plan})
        
        console.print(Panel(
            f"🚀 To upgrade to the {plan} plan, visit:\n\n"
            f"[bold blue]{upgrade_info['upgrade_url']}[/bold blue]\n\n"
            f"After completing the upgrade, your new limits will be active immediately.",
            title="Account Upgrade",
            border_style="green"
        ))
        
    except Exception as e:
        console.print(Panel(
            f"❌ Failed to process upgrade: {str(e)}",
            title="Upgrade Error",
            border_style="red"
        ))
