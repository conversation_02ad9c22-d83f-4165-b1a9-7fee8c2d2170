version: "1.0"
project:
  name: "my-performance-audit"
  description: "Performance auditing project using Complexity Relativity Theorem"

profiles:
  default:
    docker_image: "exoPiper/agent:latest"
    workloads:
      - name: "mergesort"
        type: "sorting"
        parameters:
          min_size: 1000
          max_size: 100000
          steps: 10
          data_type: "random"
        enabled: true
        timeout: 300
        iterations: 5
      
      - name: "3sat"
        type: "sat_solver"
        parameters:
          min_variables: 50
          max_variables: 500
          steps: 8
          clause_ratio: 4.3
        enabled: true
        timeout: 600
        iterations: 3
      
      - name: "matrix_multiply"
        type: "linear_algebra"
        parameters:
          min_size: 100
          max_size: 2000
          steps: 8
          precision: "float32"
        enabled: false
        timeout: 400
        iterations: 5

  quick:
    docker_image: "exoPiper/agent:latest"
    workloads:
      - name: "mergesort"
        type: "sorting"
        parameters:
          min_size: 1000
          max_size: 10000
          steps: 5
        enabled: true
        timeout: 60
        iterations: 3

  comprehensive:
    docker_image: "exoPiper/agent:latest"
    workloads:
      - name: "mergesort"
        type: "sorting"
        parameters:
          min_size: 1000
          max_size: 1000000
          steps: 15
        enabled: true
        timeout: 600
        iterations: 10
      
      - name: "3sat"
        type: "sat_solver"
        parameters:
          min_variables: 50
          max_variables: 1000
          steps: 12
        enabled: true
        timeout: 1200
        iterations: 5
      
      - name: "matrix_multiply"
        type: "linear_algebra"
        parameters:
          min_size: 100
          max_size: 3000
          steps: 10
        enabled: true
        timeout: 800
        iterations: 5
      
      - name: "fft"
        type: "signal_processing"
        parameters:
          min_size: 1024
          max_size: 1048576
          steps: 10
        enabled: true
        timeout: 400
        iterations: 5

settings:
  parallel_jobs: 2
  output_format: "json"
  log_level: "info"
  retry_failed: true
  max_retries: 2
