"""
Report management CLI commands
"""

import click
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from datetime import datetime

from ..core.config import Config
from ..core.client import ExoPiperClient

console = Console()

@click.group(name='reports')
def reports_group():
    """Report generation and management commands"""
    pass

@reports_group.command()
@click.option('--limit', '-l', type=int, default=10, help='Number of reports to show')
@click.pass_context
def list(ctx, limit):
    """List available reports"""
    config = ctx.obj['config']
    
    if not config.is_authenticated():
        console.print(Panel(
            "❌ Not authenticated. Please run [bold]perf-audit auth login[/bold] first.",
            title="Authentication Required",
            border_style="red"
        ))
        return
    
    try:
        client = ExoPiperClient(config)
        reports = client.get_reports(limit=limit)
        
        if not reports:
            console.print(Panel(
                "No reports found. Run some benchmarks first!",
                title="Reports",
                border_style="yellow"
            ))
            return
        
        table = Table(title="Available Reports")
        table.add_column("ID", style="cyan")
        table.add_column("Name", style="green")
        table.add_column("Created", style="blue")
        table.add_column("Workloads", style="yellow")
        table.add_column("Status", style="white")
        
        for report in reports:
            created_date = datetime.fromisoformat(report['created_at'].replace('Z', '+00:00'))
            formatted_date = created_date.strftime('%Y-%m-%d %H:%M')
            
            workloads = ', '.join(report.get('workloads', []))
            if len(workloads) > 30:
                workloads = workloads[:27] + "..."
            
            status = "✅ Ready" if report['status'] == 'completed' else f"⏳ {report['status']}"
            
            table.add_row(
                report['id'][:8],
                report['name'],
                formatted_date,
                workloads,
                status
            )
        
        console.print(table)
        console.print(f"\nShowing {len(reports)} of {limit} reports")
        console.print("Use [bold]perf-audit reports show <id>[/bold] to view details")
        
    except Exception as e:
        console.print(Panel(
            f"❌ Failed to list reports: {str(e)}",
            title="Error",
            border_style="red"
        ))

@reports_group.command()
@click.argument('report_id')
@click.pass_context
def show(ctx, report_id):
    """Show detailed report information"""
    config = ctx.obj['config']
    
    if not config.is_authenticated():
        console.print(Panel(
            "❌ Not authenticated. Please run [bold]perf-audit auth login[/bold] first.",
            title="Authentication Required",
            border_style="red"
        ))
        return
    
    try:
        client = ExoPiperClient(config)
        report = client.get_report(report_id)
        
        # Display report summary
        table = Table(title=f"Report Details: {report['name']}")
        table.add_column("Property", style="cyan")
        table.add_column("Value", style="white")
        
        created_date = datetime.fromisoformat(report['created_at'].replace('Z', '+00:00'))
        
        table.add_row("ID", report['id'])
        table.add_row("Name", report['name'])
        table.add_row("Status", report['status'])
        table.add_row("Created", created_date.strftime('%Y-%m-%d %H:%M:%S UTC'))
        table.add_row("Workloads", ', '.join(report.get('workloads', [])))
        table.add_row("Profile", report.get('profile', 'default'))
        
        if 'summary' in report:
            summary = report['summary']
            table.add_row("Total Jobs", str(summary.get('total_jobs', 0)))
            table.add_row("Successful", str(summary.get('successful_jobs', 0)))
            table.add_row("Failed", str(summary.get('failed_jobs', 0)))
            table.add_row("Avg Duration", f"{summary.get('avg_duration', 0):.2f}s")
        
        console.print(table)
        
        # Display workload results if available
        if 'results' in report and report['results']:
            results_table = Table(title="Workload Results")
            results_table.add_column("Workload", style="cyan")
            results_table.add_column("λ (Lambda)", style="yellow")
            results_table.add_column("p (Exponent)", style="magenta")
            results_table.add_column("R²", style="green")
            results_table.add_column("Status", style="white")
            
            for result in report['results']:
                status = "✅ Success" if result.get('success') else "❌ Failed"
                lambda_val = f"{result.get('lambda', 'N/A'):.6f}" if result.get('lambda') else "N/A"
                exponent_val = f"{result.get('exponent', 'N/A'):.4f}" if result.get('exponent') else "N/A"
                r_squared_val = f"{result.get('r_squared', 'N/A'):.4f}" if result.get('r_squared') else "N/A"
                
                results_table.add_row(
                    result['workload'],
                    lambda_val,
                    exponent_val,
                    r_squared_val,
                    status
                )
            
            console.print(results_table)
        
        console.print(f"\nTo download this report: [bold]perf-audit reports download {report_id}[/bold]")
        
    except Exception as e:
        console.print(Panel(
            f"❌ Failed to show report: {str(e)}",
            title="Error",
            border_style="red"
        ))

@reports_group.command()
@click.argument('report_id')
@click.option('--output', '-o', type=click.Path(), help='Output file path')
@click.option('--format', 'output_format', type=click.Choice(['pdf', 'html', 'json']), 
              default='pdf', help='Output format')
@click.pass_context
def download(ctx, report_id, output, output_format):
    """Download a report file"""
    config = ctx.obj['config']
    
    if not config.is_authenticated():
        console.print(Panel(
            "❌ Not authenticated. Please run [bold]perf-audit auth login[/bold] first.",
            title="Authentication Required",
            border_style="red"
        ))
        return
    
    try:
        client = ExoPiperClient(config)
        
        # Get report info first
        report = client.get_report(report_id)
        
        # Determine output path
        if not output:
            report_name = report['name'].replace(' ', '_').lower()
            output = f"{report_name}_{report_id[:8]}.{output_format}"
        
        output_path = Path(output)
        
        # Download report
        with console.status(f"Downloading report to {output_path}..."):
            client.download_report(report_id, output_path)
        
        console.print(Panel(
            f"✅ Report downloaded successfully\n"
            f"File: [bold]{output_path.absolute()}[/bold]\n"
            f"Size: {output_path.stat().st_size:,} bytes",
            title="Download Complete",
            border_style="green"
        ))
        
    except Exception as e:
        console.print(Panel(
            f"❌ Failed to download report: {str(e)}",
            title="Error",
            border_style="red"
        ))

@reports_group.command()
@click.option('--workload', '-w', help='Filter by workload name')
@click.option('--days', '-d', type=int, default=30, help='Number of days to look back')
@click.option('--format', 'output_format', type=click.Choice(['table', 'json']), 
              default='table', help='Output format')
@click.pass_context
def history(ctx, workload, days, output_format):
    """Show benchmark execution history"""
    config = ctx.obj['config']
    
    if not config.is_authenticated():
        console.print(Panel(
            "❌ Not authenticated. Please run [bold]perf-audit auth login[/bold] first.",
            title="Authentication Required",
            border_style="red"
        ))
        return
    
    try:
        client = ExoPiperClient(config)
        history_data = client.get_benchmark_history(workload=workload, days=days)
        
        if not history_data:
            filter_msg = f" for workload '{workload}'" if workload else ""
            console.print(Panel(
                f"No benchmark history found{filter_msg} in the last {days} days",
                title="History",
                border_style="yellow"
            ))
            return
        
        if output_format == 'json':
            import json
            console.print(json.dumps(history_data, indent=2))
            return
        
        # Display as table
        table = Table(title=f"Benchmark History ({days} days)")
        table.add_column("Date", style="cyan")
        table.add_column("Workload", style="green")
        table.add_column("λ (Lambda)", style="yellow")
        table.add_column("p (Exponent)", style="magenta")
        table.add_column("Duration", style="blue")
        table.add_column("Status", style="white")
        
        for entry in history_data:
            exec_date = datetime.fromisoformat(entry['executed_at'].replace('Z', '+00:00'))
            formatted_date = exec_date.strftime('%m-%d %H:%M')
            
            status = "✅" if entry.get('success') else "❌"
            lambda_val = f"{entry.get('lambda', 'N/A'):.4f}" if entry.get('lambda') else "N/A"
            exponent_val = f"{entry.get('exponent', 'N/A'):.3f}" if entry.get('exponent') else "N/A"
            duration = f"{entry.get('duration', 0):.1f}s" if entry.get('duration') else "N/A"
            
            table.add_row(
                formatted_date,
                entry['workload'],
                lambda_val,
                exponent_val,
                duration,
                status
            )
        
        console.print(table)
        console.print(f"\nShowing {len(history_data)} benchmark executions")
        
    except Exception as e:
        console.print(Panel(
            f"❌ Failed to get history: {str(e)}",
            title="Error",
            border_style="red"
        ))
