"""
Configuration management for Exo Piper CLI
"""

import os
import yaml
import json
from pathlib import Path
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
import click
from cryptography.fernet import Fernet
import base64

class WorkloadConfig(BaseModel):
    """Configuration for a single workload"""
    name: str
    type: str  # mergesort, 3sat, mlmodel, etc.
    parameters: Dict[str, Any] = Field(default_factory=dict)
    enabled: bool = True
    timeout: int = 300  # seconds
    iterations: int = 5
    
class ProfileConfig(BaseModel):
    """Configuration profile containing multiple workloads"""
    name: str
    workloads: List[WorkloadConfig]
    docker_image: str = "exoPiper/agent:latest"
    environment: Dict[str, str] = Field(default_factory=dict)
    
class Config:
    """Main configuration class for Exo Piper CLI"""
    
    DEFAULT_API_URL = "https://api.exoPiper.io"
    CONFIG_DIR = Path.home() / ".exoPiper"
    CREDENTIALS_FILE = CONFIG_DIR / "credentials.json"
    PROJECT_CONFIG_FILE = "perfconfig.yaml"
    
    def __init__(self, config_path: Optional[str] = None, verbose: bool = False):
        self.verbose = verbose
        self.config_path = Path(config_path) if config_path else Path.cwd() / self.PROJECT_CONFIG_FILE
        self.api_url = self.DEFAULT_API_URL
        
        # Ensure config directory exists
        self.CONFIG_DIR.mkdir(exist_ok=True)
        
        # Load project configuration if it exists
        self._project_config = self._load_project_config()
        
    def _load_project_config(self) -> Dict[str, Any]:
        """Load project configuration from perfconfig.yaml"""
        if self.config_path.exists():
            try:
                with open(self.config_path, 'r') as f:
                    return yaml.safe_load(f) or {}
            except Exception as e:
                if self.verbose:
                    click.echo(f"Warning: Could not load config from {self.config_path}: {e}")
                return {}
        return {}
    
    def _get_encryption_key(self) -> bytes:
        """Get or create encryption key for credentials"""
        key_file = self.CONFIG_DIR / "key"
        if key_file.exists():
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(key)
            # Set restrictive permissions
            os.chmod(key_file, 0o600)
            return key
    
    def save_credentials(self, api_key: str) -> None:
        """Save encrypted API credentials"""
        key = self._get_encryption_key()
        fernet = Fernet(key)
        
        credentials = {
            "api_key": api_key,
            "api_url": self.api_url
        }
        
        encrypted_data = fernet.encrypt(json.dumps(credentials).encode())
        
        with open(self.CREDENTIALS_FILE, 'wb') as f:
            f.write(encrypted_data)
        
        # Set restrictive permissions
        os.chmod(self.CREDENTIALS_FILE, 0o600)
    
    def load_credentials(self) -> Optional[Dict[str, str]]:
        """Load and decrypt API credentials"""
        if not self.CREDENTIALS_FILE.exists():
            return None
        
        try:
            key = self._get_encryption_key()
            fernet = Fernet(key)
            
            with open(self.CREDENTIALS_FILE, 'rb') as f:
                encrypted_data = f.read()
            
            decrypted_data = fernet.decrypt(encrypted_data)
            return json.loads(decrypted_data.decode())
        
        except Exception as e:
            if self.verbose:
                click.echo(f"Warning: Could not load credentials: {e}")
            return None
    
    def is_authenticated(self) -> bool:
        """Check if user is authenticated"""
        credentials = self.load_credentials()
        return credentials is not None and 'api_key' in credentials
    
    def get_api_key(self) -> Optional[str]:
        """Get the stored API key"""
        credentials = self.load_credentials()
        return credentials.get('api_key') if credentials else None
    
    def init_project(self) -> Path:
        """Initialize a new project configuration"""
        if self.config_path.exists():
            raise click.ClickException(f"Configuration file already exists: {self.config_path}")
        
        # Create default configuration
        default_config = {
            "version": "1.0",
            "project": {
                "name": "my-performance-audit",
                "description": "Performance auditing project"
            },
            "profiles": {
                "default": {
                    "docker_image": "exoPiper/agent:latest",
                    "workloads": [
                        {
                            "name": "mergesort",
                            "type": "sorting",
                            "parameters": {
                                "min_size": 1000,
                                "max_size": 100000,
                                "steps": 10
                            },
                            "enabled": True,
                            "timeout": 300,
                            "iterations": 5
                        },
                        {
                            "name": "3sat",
                            "type": "sat_solver",
                            "parameters": {
                                "min_variables": 50,
                                "max_variables": 500,
                                "steps": 8
                            },
                            "enabled": True,
                            "timeout": 600,
                            "iterations": 3
                        }
                    ]
                },
                "quick": {
                    "docker_image": "exoPiper/agent:latest",
                    "workloads": [
                        {
                            "name": "mergesort",
                            "type": "sorting",
                            "parameters": {
                                "min_size": 1000,
                                "max_size": 10000,
                                "steps": 5
                            },
                            "enabled": True,
                            "timeout": 60,
                            "iterations": 3
                        }
                    ]
                }
            },
            "settings": {
                "parallel_jobs": 2,
                "output_format": "json",
                "log_level": "info"
            }
        }
        
        with open(self.config_path, 'w') as f:
            yaml.dump(default_config, f, default_flow_style=False, indent=2)
        
        return self.config_path
    
    def get_profiles(self) -> List[str]:
        """Get list of available profiles"""
        return list(self._project_config.get('profiles', {}).keys())
    
    def get_profile(self, profile_name: str) -> ProfileConfig:
        """Get configuration for a specific profile"""
        profiles = self._project_config.get('profiles', {})
        if profile_name not in profiles:
            raise click.ClickException(f"Profile '{profile_name}' not found")
        
        profile_data = profiles[profile_name]
        return ProfileConfig(
            name=profile_name,
            workloads=[WorkloadConfig(**w) for w in profile_data.get('workloads', [])],
            docker_image=profile_data.get('docker_image', 'exoPiper/agent:latest'),
            environment=profile_data.get('environment', {})
        )
    
    def get_default_workloads(self) -> List[str]:
        """Get list of default workloads from the default profile"""
        try:
            default_profile = self.get_profile('default')
            return [w.name for w in default_profile.workloads if w.enabled]
        except:
            return ['mergesort', '3sat']
    
    def get_setting(self, key: str, default: Any = None) -> Any:
        """Get a setting value"""
        return self._project_config.get('settings', {}).get(key, default)
