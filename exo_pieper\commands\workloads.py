"""
Workload management CLI commands
"""

import click
import yaml
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.syntax import Syntax

from ..core.config import Config
from ..core.client import ExoPiperClient

console = Console()

@click.group(name='workloads')
def workloads_group():
    """Workload configuration and management commands"""
    pass

@workloads_group.command()
@click.option('--profile', '-p', default='default', help='Configuration profile to list')
@click.pass_context
def list(ctx, profile):
    """List configured workloads"""
    config = ctx.obj['config']
    
    try:
        profile_config = config.get_profile(profile)
        
        if not profile_config.workloads:
            console.print(Panel(
                f"No workloads configured in profile '{profile}'",
                title="Workloads",
                border_style="yellow"
            ))
            return
        
        table = Table(title=f"Workloads in Profile: {profile}")
        table.add_column("Name", style="cyan")
        table.add_column("Type", style="green")
        table.add_column("Enabled", style="yellow")
        table.add_column("Timeout", style="blue")
        table.add_column("Iterations", style="magenta")
        table.add_column("Parameters", style="white")
        
        for workload in profile_config.workloads:
            enabled = "✅ Yes" if workload.enabled else "❌ No"
            params = ", ".join([f"{k}={v}" for k, v in workload.parameters.items()])
            if len(params) > 50:
                params = params[:47] + "..."
            
            table.add_row(
                workload.name,
                workload.type,
                enabled,
                f"{workload.timeout}s",
                str(workload.iterations),
                params
            )
        
        console.print(table)
        
    except Exception as e:
        console.print(Panel(
            f"❌ Failed to list workloads: {str(e)}",
            title="Error",
            border_style="red"
        ))

@workloads_group.command()
@click.pass_context
def templates(ctx):
    """Show available workload templates"""
    config = ctx.obj['config']
    
    if not config.is_authenticated():
        console.print(Panel(
            "❌ Not authenticated. Please run [bold]perf-audit auth login[/bold] first.",
            title="Authentication Required",
            border_style="red"
        ))
        return
    
    try:
        client = ExoPiperClient(config)
        templates = client.get_workload_templates()
        
        if not templates:
            console.print(Panel(
                "No workload templates available",
                title="Templates",
                border_style="yellow"
            ))
            return
        
        table = Table(title="Available Workload Templates")
        table.add_column("Name", style="cyan")
        table.add_column("Type", style="green")
        table.add_column("Description", style="white")
        table.add_column("Complexity", style="yellow")
        
        for template in templates:
            table.add_row(
                template['name'],
                template['type'],
                template['description'],
                template.get('complexity_class', 'Unknown')
            )
        
        console.print(table)
        console.print("\nTo add a template to your configuration:")
        console.print("[bold]perf-audit workloads add --template <template_name>[/bold]")
        
    except Exception as e:
        console.print(Panel(
            f"❌ Failed to get templates: {str(e)}",
            title="Error",
            border_style="red"
        ))

@workloads_group.command()
@click.argument('name')
@click.option('--type', '-t', required=True, help='Workload type')
@click.option('--template', help='Use template as base')
@click.option('--profile', '-p', default='default', help='Profile to add to')
@click.option('--enabled/--disabled', default=True, help='Enable workload')
@click.option('--timeout', type=int, default=300, help='Timeout in seconds')
@click.option('--iterations', type=int, default=5, help='Number of iterations')
@click.pass_context
def add(ctx, name, type, template, profile, enabled, timeout, iterations):
    """Add a new workload to configuration"""
    config = ctx.obj['config']
    
    if not config.config_path.exists():
        console.print(Panel(
            "❌ No configuration file found. Run [bold]perf-audit init[/bold] first.",
            title="Configuration Error",
            border_style="red"
        ))
        return
    
    try:
        # Load current configuration
        with open(config.config_path, 'r') as f:
            config_data = yaml.safe_load(f)
        
        # Ensure profile exists
        if 'profiles' not in config_data:
            config_data['profiles'] = {}
        if profile not in config_data['profiles']:
            config_data['profiles'][profile] = {'workloads': []}
        if 'workloads' not in config_data['profiles'][profile]:
            config_data['profiles'][profile]['workloads'] = []
        
        # Check if workload already exists
        existing_names = [w['name'] for w in config_data['profiles'][profile]['workloads']]
        if name in existing_names:
            console.print(Panel(
                f"❌ Workload '{name}' already exists in profile '{profile}'",
                title="Configuration Error",
                border_style="red"
            ))
            return
        
        # Get template if specified
        workload_config = {
            'name': name,
            'type': type,
            'enabled': enabled,
            'timeout': timeout,
            'iterations': iterations,
            'parameters': {}
        }
        
        if template and config.is_authenticated():
            try:
                client = ExoPiperClient(config)
                templates = client.get_workload_templates()
                template_data = next((t for t in templates if t['name'] == template), None)
                
                if template_data:
                    workload_config['parameters'] = template_data.get('default_parameters', {})
                    workload_config['type'] = template_data['type']
                    console.print(f"✅ Using template: {template}")
                else:
                    console.print(f"⚠️ Template '{template}' not found, using defaults")
            except:
                console.print(f"⚠️ Could not load template '{template}', using defaults")
        
        # Add workload to configuration
        config_data['profiles'][profile]['workloads'].append(workload_config)
        
        # Save configuration
        with open(config.config_path, 'w') as f:
            yaml.dump(config_data, f, default_flow_style=False, indent=2)
        
        console.print(Panel(
            f"✅ Added workload '{name}' to profile '{profile}'\n"
            f"Type: {type}\n"
            f"Enabled: {'Yes' if enabled else 'No'}\n"
            f"Timeout: {timeout}s\n"
            f"Iterations: {iterations}",
            title="Workload Added",
            border_style="green"
        ))
        
    except Exception as e:
        console.print(Panel(
            f"❌ Failed to add workload: {str(e)}",
            title="Error",
            border_style="red"
        ))

@workloads_group.command()
@click.argument('name')
@click.option('--profile', '-p', default='default', help='Profile to remove from')
@click.option('--confirm', is_flag=True, help='Skip confirmation prompt')
@click.pass_context
def remove(ctx, name, profile, confirm):
    """Remove a workload from configuration"""
    config = ctx.obj['config']
    
    if not config.config_path.exists():
        console.print(Panel(
            "❌ No configuration file found.",
            title="Configuration Error",
            border_style="red"
        ))
        return
    
    try:
        # Load current configuration
        with open(config.config_path, 'r') as f:
            config_data = yaml.safe_load(f)
        
        # Check if profile and workload exist
        if (profile not in config_data.get('profiles', {}) or
            'workloads' not in config_data['profiles'][profile]):
            console.print(Panel(
                f"❌ Profile '{profile}' not found",
                title="Configuration Error",
                border_style="red"
            ))
            return
        
        workloads = config_data['profiles'][profile]['workloads']
        workload_index = None
        
        for i, workload in enumerate(workloads):
            if workload['name'] == name:
                workload_index = i
                break
        
        if workload_index is None:
            console.print(Panel(
                f"❌ Workload '{name}' not found in profile '{profile}'",
                title="Configuration Error",
                border_style="red"
            ))
            return
        
        # Confirm removal
        if not confirm:
            if not click.confirm(f"Remove workload '{name}' from profile '{profile}'?"):
                console.print("Operation cancelled.")
                return
        
        # Remove workload
        removed_workload = workloads.pop(workload_index)
        
        # Save configuration
        with open(config.config_path, 'w') as f:
            yaml.dump(config_data, f, default_flow_style=False, indent=2)
        
        console.print(Panel(
            f"✅ Removed workload '{name}' from profile '{profile}'",
            title="Workload Removed",
            border_style="green"
        ))
        
    except Exception as e:
        console.print(Panel(
            f"❌ Failed to remove workload: {str(e)}",
            title="Error",
            border_style="red"
        ))

@workloads_group.command()
@click.argument('name')
@click.option('--profile', '-p', default='default', help='Profile containing the workload')
@click.pass_context
def show(ctx, name, profile):
    """Show detailed workload configuration"""
    config = ctx.obj['config']
    
    try:
        profile_config = config.get_profile(profile)
        workload = None
        
        for w in profile_config.workloads:
            if w.name == name:
                workload = w
                break
        
        if not workload:
            console.print(Panel(
                f"❌ Workload '{name}' not found in profile '{profile}'",
                title="Workload Not Found",
                border_style="red"
            ))
            return
        
        # Display workload details
        workload_yaml = yaml.dump({
            'name': workload.name,
            'type': workload.type,
            'enabled': workload.enabled,
            'timeout': workload.timeout,
            'iterations': workload.iterations,
            'parameters': workload.parameters
        }, default_flow_style=False, indent=2)
        
        syntax = Syntax(workload_yaml, "yaml", theme="monokai", line_numbers=True)
        
        console.print(Panel(
            syntax,
            title=f"Workload Configuration: {name}",
            border_style="blue"
        ))
        
    except Exception as e:
        console.print(Panel(
            f"❌ Failed to show workload: {str(e)}",
            title="Error",
            border_style="red"
        ))
