#!/usr/bin/env python3
"""
Main CLI interface for Exo Piper Performance Auditing Tool
"""

import click
import sys
import os
from pathlib import Path
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text

from .core.config import Config
from .core.client import ExoPiperClient

# Import BenchmarkRunner only if available
try:
    from .core.benchmark import BenchmarkRunner
    BENCHMARK_AVAILABLE = True
except ImportError:
    BenchmarkRunner = None
    BENCHMARK_AVAILABLE = False

from .commands.auth import auth_group
from .commands.workloads import workloads_group
from .commands.reports import reports_group
from .commands.schedule import schedule_group

console = Console()

@click.group()
@click.version_option(version="0.1.0", prog_name="exo-Piper")
@click.option('--config', '-c', type=click.Path(), help='Path to configuration file')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose output')
@click.option('--api-url', help='Override API URL')
@click.pass_context
def cli(ctx, config, verbose, api_url):
    """
    Exo Piper - Performance Auditing CLI Tool

    Automated performance benchmarking and analysis using the Complexity Relativity Theorem.
    """
    ctx.ensure_object(dict)

    # Initialize configuration
    config_obj = Config(config_path=config, verbose=verbose)
    if api_url:
        config_obj.api_url = api_url

    ctx.obj['config'] = config_obj
    ctx.obj['console'] = console

@cli.command()
@click.option('--api-key', prompt=True, hide_input=True, help='Your Exo Piper API key')
@click.pass_context
def login(ctx, api_key):
    """Authenticate with Exo Piper service"""
    config = ctx.obj['config']
    client = ExoPiperClient(config)

    try:
        # Validate API key
        user_info = client.authenticate(api_key)

        # Save credentials
        config.save_credentials(api_key)

        console.print(Panel(
            f"✅ Successfully authenticated as [bold]{user_info['email']}[/bold]\n"
            f"Plan: [bold]{user_info['plan']}[/bold]\n"
            f"Workloads remaining: [bold]{user_info['workloads_remaining']}/{user_info['workloads_limit']}[/bold]",
            title="Authentication Success",
            border_style="green"
        ))

    except Exception as e:
        console.print(Panel(
            f"❌ Authentication failed: {str(e)}",
            title="Authentication Error",
            border_style="red"
        ))
        sys.exit(1)

@cli.command()
@click.pass_context
def init(ctx):
    """Initialize Exo Piper configuration in current directory"""
    config = ctx.obj['config']

    try:
        config_path = config.init_project()
        console.print(Panel(
            f"✅ Initialized Exo Piper configuration\n"
            f"Configuration file: [bold]{config_path}[/bold]\n\n"
            f"Next steps:\n"
            f"1. Edit [bold]perfconfig.yaml[/bold] to configure your workloads\n"
            f"2. Run [bold]perf-audit run[/bold] to start benchmarking",
            title="Project Initialized",
            border_style="green"
        ))

    except Exception as e:
        console.print(Panel(
            f"❌ Initialization failed: {str(e)}",
            title="Initialization Error",
            border_style="red"
        ))
        sys.exit(1)

@cli.command()
@click.option('--workloads', '-w', help='Comma-separated list of workloads to run')
@click.option('--profile', '-p', default='default', help='Configuration profile to use')
@click.option('--dry-run', is_flag=True, help='Show what would be executed without running')
@click.pass_context
def run(ctx, workloads, profile, dry_run):
    """Execute performance benchmarks"""
    config = ctx.obj['config']

    if not BENCHMARK_AVAILABLE:
        console.print(Panel(
            "❌ Benchmark functionality not available. Please install dependencies:\n"
            "[bold]pip install -r requirements.txt[/bold]",
            title="Dependencies Missing",
            border_style="red"
        ))
        sys.exit(1)

    if not config.is_authenticated():
        console.print(Panel(
            "❌ Not authenticated. Please run [bold]perf-audit login[/bold] first.",
            title="Authentication Required",
            border_style="red"
        ))
        sys.exit(1)

    try:
        client = ExoPiperClient(config)
        runner = BenchmarkRunner(config, client)

        # Parse workloads
        if workloads:
            workload_list = [w.strip() for w in workloads.split(',')]
        else:
            workload_list = config.get_default_workloads()

        if dry_run:
            console.print(Panel(
                f"Would execute workloads: [bold]{', '.join(workload_list)}[/bold]\n"
                f"Profile: [bold]{profile}[/bold]",
                title="Dry Run",
                border_style="blue"
            ))
            return

        # Execute benchmarks
        results = runner.run_benchmarks(workload_list, profile)

        # Display results summary
        table = Table(title="Benchmark Results")
        table.add_column("Workload", style="cyan")
        table.add_column("Status", style="green")
        table.add_column("λ (Lambda)", style="yellow")
        table.add_column("p (Exponent)", style="magenta")
        table.add_column("Duration", style="blue")

        for result in results:
            status = "✅ Success" if result['success'] else "❌ Failed"
            table.add_row(
                result['workload'],
                status,
                f"{result.get('lambda', 'N/A')}",
                f"{result.get('exponent', 'N/A')}",
                f"{result.get('duration', 'N/A')}s"
            )

        console.print(table)

    except Exception as e:
        console.print(Panel(
            f"❌ Benchmark execution failed: {str(e)}",
            title="Execution Error",
            border_style="red"
        ))
        sys.exit(1)

@cli.command()
@click.pass_context
def status(ctx):
    """Show current status and account information"""
    config = ctx.obj['config']

    if not config.is_authenticated():
        console.print(Panel(
            "❌ Not authenticated. Please run [bold]perf-audit login[/bold] first.",
            title="Authentication Required",
            border_style="red"
        ))
        sys.exit(1)

    try:
        client = ExoPiperClient(config)
        status_info = client.get_status()

        table = Table(title="Account Status")
        table.add_column("Property", style="cyan")
        table.add_column("Value", style="white")

        table.add_row("Email", status_info['email'])
        table.add_row("Plan", status_info['plan'])
        table.add_row("Workloads Used", f"{status_info['workloads_used']}/{status_info['workloads_limit']}")
        table.add_row("Data Retention", f"{status_info['retention_days']} days")
        table.add_row("Last Benchmark", status_info.get('last_benchmark', 'Never'))

        console.print(table)

    except Exception as e:
        console.print(Panel(
            f"❌ Failed to get status: {str(e)}",
            title="Status Error",
            border_style="red"
        ))
        sys.exit(1)

# Add command groups
cli.add_command(auth_group)
cli.add_command(workloads_group)
cli.add_command(reports_group)
cli.add_command(schedule_group)

def main():
    """Entry point for the CLI"""
    cli()

if __name__ == '__main__':
    main()
