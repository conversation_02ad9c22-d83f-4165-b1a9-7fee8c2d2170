"""
Exo Piper - Performance Auditing CLI Tool

A comprehensive performance auditing tool implementing the Complexity Relativity Theorem
for automated benchmark analysis and scalability assessment.
"""

__version__ = "0.1.0"
__author__ = "Exo Piper Team"
__email__ = "<EMAIL>"

# Import only core modules that don't require heavy dependencies
try:
    from .core.config import Config
    from .core.client import ExoPiperClient
    __all__ = ["Config", "ExoPiperClient"]
except ImportError:
    # Allow package to be imported even if dependencies are missing
    __all__ = []

# Import BenchmarkRunner only if docker is available
try:
    from .core.benchmark import BenchmarkRunner
    __all__.append("BenchmarkRunner")
except ImportError:
    pass
