#!/usr/bin/env python3
"""
Mergesort benchmark implementation for complexity analysis
"""

import time
import random
import json
import sys
from typing import List, Dict, Any
import numpy as np

def mergesort(arr: List[int]) -> List[int]:
    """
    Standard mergesort implementation - O(n log n) complexity
    """
    if len(arr) <= 1:
        return arr
    
    mid = len(arr) // 2
    left = mergesort(arr[:mid])
    right = mergesort(arr[mid:])
    
    return merge(left, right)

def merge(left: List[int], right: List[int]) -> List[int]:
    """
    Merge two sorted arrays
    """
    result = []
    i = j = 0
    
    while i < len(left) and j < len(right):
        if left[i] <= right[j]:
            result.append(left[i])
            i += 1
        else:
            result.append(right[j])
            j += 1
    
    result.extend(left[i:])
    result.extend(right[j:])
    return result

def generate_test_data(size: int, data_type: str = "random") -> List[int]:
    """
    Generate test data for sorting
    """
    if data_type == "random":
        return [random.randint(1, size * 10) for _ in range(size)]
    elif data_type == "sorted":
        return list(range(size))
    elif data_type == "reverse":
        return list(range(size, 0, -1))
    elif data_type == "nearly_sorted":
        arr = list(range(size))
        # Swap 5% of elements randomly
        swaps = max(1, size // 20)
        for _ in range(swaps):
            i, j = random.randint(0, size-1), random.randint(0, size-1)
            arr[i], arr[j] = arr[j], arr[i]
        return arr
    else:
        return [random.randint(1, size * 10) for _ in range(size)]

def benchmark_mergesort(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Run mergesort benchmark with varying input sizes
    """
    min_size = config.get("min_size", 1000)
    max_size = config.get("max_size", 100000)
    steps = config.get("steps", 10)
    iterations = config.get("iterations", 5)
    data_type = config.get("data_type", "random")
    
    # Generate logarithmic size progression
    if steps == 1:
        sizes = [max_size]
    else:
        log_min = np.log(min_size)
        log_max = np.log(max_size)
        log_sizes = np.linspace(log_min, log_max, steps)
        sizes = [int(np.exp(log_size)) for log_size in log_sizes]
    
    results = {
        "algorithm": "mergesort",
        "complexity_class": "O(n log n)",
        "input_sizes": [],
        "execution_times": [],
        "iterations_per_size": iterations,
        "data_type": data_type,
        "raw_measurements": []
    }
    
    print(f"Running mergesort benchmark: {min_size} to {max_size}, {steps} steps, {iterations} iterations")
    
    for size in sizes:
        print(f"Testing size: {size}")
        size_times = []
        
        for iteration in range(iterations):
            # Generate fresh data for each iteration
            test_data = generate_test_data(size, data_type)
            
            # Measure execution time
            start_time = time.perf_counter()
            sorted_data = mergesort(test_data.copy())
            end_time = time.perf_counter()
            
            execution_time = end_time - start_time
            size_times.append(execution_time)
            
            # Verify correctness
            if not is_sorted(sorted_data):
                raise ValueError(f"Sort failed for size {size}, iteration {iteration}")
        
        # Calculate statistics for this size
        avg_time = np.mean(size_times)
        std_time = np.std(size_times)
        min_time = np.min(size_times)
        max_time = np.max(size_times)
        
        results["input_sizes"].append(size)
        results["execution_times"].append(avg_time)
        results["raw_measurements"].append({
            "size": size,
            "times": size_times,
            "avg": avg_time,
            "std": std_time,
            "min": min_time,
            "max": max_time
        })
        
        print(f"  Size {size}: {avg_time:.6f}s ± {std_time:.6f}s")
    
    return results

def is_sorted(arr: List[int]) -> bool:
    """
    Verify that array is sorted
    """
    return all(arr[i] <= arr[i+1] for i in range(len(arr)-1))

def main():
    """
    Main entry point for standalone execution
    """
    if len(sys.argv) > 1:
        config_file = sys.argv[1]
        with open(config_file, 'r') as f:
            config = json.load(f)
    else:
        # Default configuration
        config = {
            "min_size": 1000,
            "max_size": 100000,
            "steps": 10,
            "iterations": 5,
            "data_type": "random"
        }
    
    try:
        results = benchmark_mergesort(config)
        
        # Output results as JSON
        output_file = config.get("output_file", "mergesort_results.json")
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"Results saved to {output_file}")
        
    except Exception as e:
        print(f"Benchmark failed: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
