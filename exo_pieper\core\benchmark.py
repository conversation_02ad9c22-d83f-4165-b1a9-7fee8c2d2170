"""
Benchmark execution and Docker agent management
"""

import docker
import json
import time
import tempfile
import shutil
from pathlib import Path
from typing import Dict, List, Any, Optional
import click
from rich.console import Console
from rich.progress import Progress, TaskID
import psutil
import platform

from .config import Config, ProfileConfig, WorkloadConfig
from .client import ExoPiperClient

console = Console()

class BenchmarkRunner:
    """Manages benchmark execution using Docker agents"""
    
    def __init__(self, config: Config, client: ExoPiperClient):
        self.config = config
        self.client = client
        self.docker_client = None
        
        # Initialize Docker client
        try:
            self.docker_client = docker.from_env()
            # Test Docker connection
            self.docker_client.ping()
        except Exception as e:
            raise click.ClickException(f"Docker not available: {str(e)}")
    
    def run_benchmarks(self, workload_names: List[str], profile_name: str) -> List[Dict[str, Any]]:
        """Execute benchmarks for specified workloads"""
        
        # Get profile configuration
        profile = self.config.get_profile(profile_name)
        
        # Filter workloads based on requested names
        workloads_to_run = [
            w for w in profile.workloads 
            if w.name in workload_names and w.enabled
        ]
        
        if not workloads_to_run:
            raise click.ClickException(f"No enabled workloads found matching: {workload_names}")
        
        # Create job on server
        job_data = self.client.create_job(
            workloads=[w.name for w in workloads_to_run],
            profile=profile_name,
            metadata=self._get_system_metadata()
        )
        
        job_id = job_data["job_id"]
        console.print(f"Created job: [bold]{job_id}[/bold]")
        
        results = []
        
        with Progress() as progress:
            main_task = progress.add_task(
                f"Running {len(workloads_to_run)} workloads...", 
                total=len(workloads_to_run)
            )
            
            for workload in workloads_to_run:
                workload_task = progress.add_task(
                    f"Executing {workload.name}...", 
                    total=workload.iterations
                )
                
                try:
                    result = self._run_single_workload(workload, profile, progress, workload_task)
                    result["job_id"] = job_id
                    
                    # Upload results to server
                    self.client.upload_benchmark_data(job_id, workload.name, result)
                    
                    results.append(result)
                    progress.update(main_task, advance=1)
                    
                except Exception as e:
                    error_result = {
                        "workload": workload.name,
                        "success": False,
                        "error": str(e),
                        "job_id": job_id
                    }
                    results.append(error_result)
                    progress.update(main_task, advance=1)
                    
                    if self.config.verbose:
                        console.print(f"❌ Workload {workload.name} failed: {e}")
        
        return results
    
    def _run_single_workload(self, workload: WorkloadConfig, profile: ProfileConfig, 
                           progress: Progress, task_id: TaskID) -> Dict[str, Any]:
        """Execute a single workload using Docker agent"""
        
        start_time = time.time()
        
        # Prepare workload configuration
        workload_config = {
            "name": workload.name,
            "type": workload.type,
            "parameters": workload.parameters,
            "iterations": workload.iterations,
            "timeout": workload.timeout
        }
        
        # Create temporary directory for results
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            config_file = temp_path / "workload.json"
            results_file = temp_path / "results.json"
            
            # Write workload configuration
            with open(config_file, 'w') as f:
                json.dump(workload_config, f, indent=2)
            
            # Prepare Docker volumes
            volumes = {
                str(temp_path): {"bind": "/workspace", "mode": "rw"}
            }
            
            # Prepare environment variables
            environment = {
                "WORKLOAD_CONFIG": "/workspace/workload.json",
                "RESULTS_OUTPUT": "/workspace/results.json",
                "EXOPiper_VERBOSE": "1" if self.config.verbose else "0",
                **profile.environment
            }
            
            try:
                # Pull Docker image if needed
                self._ensure_docker_image(profile.docker_image)
                
                # Run Docker container
                container = self.docker_client.containers.run(
                    profile.docker_image,
                    command=["python", "/app/run_benchmark.py"],
                    volumes=volumes,
                    environment=environment,
                    detach=True,
                    remove=True,
                    network_mode="none",  # Isolated execution
                    mem_limit="2g",  # Memory limit
                    cpu_quota=100000,  # CPU limit
                )
                
                # Monitor container execution
                self._monitor_container(container, workload, progress, task_id)
                
                # Wait for completion
                exit_code = container.wait()["StatusCode"]
                
                if exit_code != 0:
                    logs = container.logs().decode('utf-8')
                    raise Exception(f"Container exited with code {exit_code}: {logs}")
                
                # Read results
                if not results_file.exists():
                    raise Exception("No results file generated")
                
                with open(results_file, 'r') as f:
                    benchmark_results = json.load(f)
                
                # Calculate complexity metrics
                complexity_metrics = self._calculate_complexity_metrics(benchmark_results)
                
                duration = time.time() - start_time
                
                return {
                    "workload": workload.name,
                    "success": True,
                    "duration": round(duration, 2),
                    "iterations": workload.iterations,
                    "raw_results": benchmark_results,
                    "lambda": complexity_metrics.get("lambda"),
                    "exponent": complexity_metrics.get("exponent"),
                    "r_squared": complexity_metrics.get("r_squared"),
                    "overhead": complexity_metrics.get("overhead"),
                    "system_info": self._get_system_metadata()
                }
                
            except docker.errors.ContainerError as e:
                raise Exception(f"Container execution failed: {e}")
            except docker.errors.ImageNotFound:
                raise Exception(f"Docker image not found: {profile.docker_image}")
            except Exception as e:
                raise Exception(f"Benchmark execution failed: {e}")
    
    def _ensure_docker_image(self, image_name: str) -> None:
        """Ensure Docker image is available locally"""
        try:
            self.docker_client.images.get(image_name)
        except docker.errors.ImageNotFound:
            console.print(f"Pulling Docker image: [bold]{image_name}[/bold]")
            try:
                self.docker_client.images.pull(image_name)
            except Exception as e:
                raise click.ClickException(f"Failed to pull Docker image {image_name}: {e}")
    
    def _monitor_container(self, container, workload: WorkloadConfig, 
                          progress: Progress, task_id: TaskID) -> None:
        """Monitor container execution and update progress"""
        timeout = workload.timeout
        start_time = time.time()
        
        while container.status != "exited":
            elapsed = time.time() - start_time
            if elapsed > timeout:
                container.kill()
                raise Exception(f"Workload timeout after {timeout} seconds")
            
            # Update progress based on elapsed time
            progress_pct = min(elapsed / timeout * 100, 95)  # Cap at 95% until completion
            progress.update(task_id, completed=progress_pct)
            
            time.sleep(1)
            container.reload()
    
    def _calculate_complexity_metrics(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate lambda and exponent using Complexity Relativity Theorem"""
        try:
            import numpy as np
            from scipy import stats
            
            # Extract timing data
            sizes = results.get("input_sizes", [])
            times = results.get("execution_times", [])
            
            if len(sizes) < 3 or len(times) < 3:
                return {"error": "Insufficient data points for regression"}
            
            # Convert to numpy arrays
            sizes = np.array(sizes)
            times = np.array(times)
            
            # Remove overhead (minimum time)
            overhead = np.min(times)
            adjusted_times = times - overhead
            
            # Ensure positive values for log transformation
            adjusted_times = np.maximum(adjusted_times, 1e-9)
            
            # Log-log regression: log(T) = log(λ) + p * log(n)
            log_sizes = np.log(sizes)
            log_times = np.log(adjusted_times)
            
            # Perform linear regression on log-log data
            slope, intercept, r_value, p_value, std_err = stats.linregress(log_sizes, log_times)
            
            # Extract metrics
            lambda_value = np.exp(intercept)  # λ = e^intercept
            exponent = slope  # p = slope
            r_squared = r_value ** 2
            
            return {
                "lambda": round(lambda_value, 6),
                "exponent": round(exponent, 4),
                "r_squared": round(r_squared, 4),
                "overhead": round(overhead, 6),
                "p_value": round(p_value, 6),
                "std_error": round(std_err, 6)
            }
            
        except ImportError:
            return {"error": "scipy not available for regression analysis"}
        except Exception as e:
            return {"error": f"Regression calculation failed: {str(e)}"}
    
    def _get_system_metadata(self) -> Dict[str, Any]:
        """Collect system information for benchmark context"""
        return {
            "platform": platform.platform(),
            "processor": platform.processor(),
            "cpu_count": psutil.cpu_count(),
            "memory_gb": round(psutil.virtual_memory().total / (1024**3), 2),
            "python_version": platform.python_version(),
            "docker_version": self.docker_client.version()["Version"] if self.docker_client else None,
            "timestamp": time.time()
        }
